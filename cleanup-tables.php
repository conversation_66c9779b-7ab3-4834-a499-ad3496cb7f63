<?php
/**
 * Table Cleanup Script
 * This will safely drop all tables with proper foreign key handling
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/config.php';

// Function to safely drop all tables
function cleanupTables() {
    try {
        $db = getDB();
        
        // Make absolutely sure we're using the right database
        $db->exec("USE `" . DB_NAME . "`");
        
        echo "<h3>Cleaning Up Tables...</h3>";
        
        // Disable foreign key checks to allow dropping tables in any order
        $db->exec("SET FOREIGN_KEY_CHECKS = 0");
        echo "<p>ℹ Disabled foreign key checks</p>";
        
        // Get all tables in the database
        $stmt = $db->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (empty($tables)) {
            echo "<p>ℹ No tables found in database</p>";
        } else {
            echo "<p>Found " . count($tables) . " tables to drop</p>";
            
            foreach ($tables as $table) {
                try {
                    $db->exec("DROP TABLE IF EXISTS `$table`");
                    echo "<p class='success'>✓ Dropped table: $table</p>";
                } catch (Exception $e) {
                    echo "<p class='error'>✗ Error dropping table $table: " . $e->getMessage() . "</p>";
                }
            }
        }
        
        // Re-enable foreign key checks
        $db->exec("SET FOREIGN_KEY_CHECKS = 1");
        echo "<p>ℹ Re-enabled foreign key checks</p>";
        
        // Verify all tables are gone
        $stmt = $db->query("SHOW TABLES");
        $remainingTables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (empty($remainingTables)) {
            echo "<p class='success'>✓ All tables successfully removed!</p>";
            return true;
        } else {
            echo "<p class='error'>✗ Some tables still remain: " . implode(', ', $remainingTables) . "</p>";
            return false;
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>✗ Error: " . $e->getMessage() . "</p>";
        return false;
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cleanup Tables</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .success { color: #155724; }
        .error { color: #721c24; }
        .btn {
            background-color: #3B82F6;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover { background-color: #2563EB; }
        .btn.danger { background-color: #DC2626; }
        .btn.danger:hover { background-color: #B91C1C; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Cleanup Tables Script</h1>
        
        <p><strong>Warning:</strong> This will drop ALL tables in the database. All data will be lost!</p>
        
        <?php
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['cleanup'])) {
            echo '<div style="border: 2px solid #DC2626; padding: 20px; margin: 20px 0; background-color: #FEF2F2;">';
            echo '<h3 style="color: #DC2626;">CLEANING UP TABLES...</h3>';
            
            if (cleanupTables()) {
                echo '<h3 style="color: #059669;">✓ ALL TABLES REMOVED SUCCESSFULLY!</h3>';
                echo '<p>You can now:</p>';
                echo '<ul>';
                echo '<li>Run <a href="fix-tables.php">Fix Tables Script</a> to recreate them</li>';
                echo '<li>Run <a href="setup.php">Setup Script</a> for full setup</li>';
                echo '<li>Manually create tables in phpMyAdmin</li>';
                echo '</ul>';
            } else {
                echo '<h3 style="color: #DC2626;">✗ CLEANUP FAILED</h3>';
                echo '<p>Check the error messages above. You may need to manually drop tables in phpMyAdmin.</p>';
            }
            
            echo '</div>';
        } else {
            echo '<p>This script will:</p>';
            echo '<ul>';
            echo '<li>Disable foreign key checks</li>';
            echo '<li>Drop ALL tables in the database</li>';
            echo '<li>Verify all tables are removed</li>';
            echo '</ul>';
            
            echo '<form method="POST">';
            echo '<button type="submit" name="cleanup" class="btn danger" onclick="return confirm(\'Are you absolutely sure? This will delete ALL tables and data!\')">Drop All Tables</button>';
            echo '</form>';
        }
        ?>
        
        <div style="margin-top: 30px;">
            <a href="fix-tables.php" class="btn">Fix Tables</a>
            <a href="setup.php" class="btn">Run Setup</a>
            <a href="debug-tables.php" class="btn">Debug Tables</a>
            <a href="index.php" class="btn">Homepage</a>
        </div>
    </div>
</body>
</html>

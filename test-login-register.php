<?php
/**
 * Simple Login/Register Test Page
 * Minimal implementation to test authentication
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/config.php';
require_once 'php/auth.php';

$message = '';
$error = '';

// Handle registration
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['register'])) {
    $name = trim($_POST['name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    
    if (empty($name) || empty($email) || empty($password)) {
        $error = 'All fields are required';
    } elseif ($password !== $confirmPassword) {
        $error = 'Passwords do not match';
    } elseif (strlen($password) < 6) {
        $error = 'Password must be at least 6 characters';
    } else {
        $result = Auth::register($name, $email, $password, $confirmPassword);
        
        if ($result['success']) {
            $message = 'Registration successful! You can now login.';
        } else {
            $error = implode(', ', $result['errors']);
        }
    }
}

// Handle login
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login'])) {
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($email) || empty($password)) {
        $error = 'Email and password are required';
    } else {
        $result = Auth::login($email, $password);
        
        if ($result['success']) {
            $message = 'Login successful! Welcome ' . $result['user']['name'];
            
            // Show redirect options
            if ($result['user']['role'] === 'admin') {
                $message .= ' <a href="admin/index.php">Go to Admin Panel</a>';
            } else {
                $message .= ' <a href="dashboard/index.php">Go to Dashboard</a>';
            }
        } else {
            $error = implode(', ', $result['errors']);
        }
    }
}

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    $message = 'Logged out successfully';
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Login/Register Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .form-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 5px;
            font-size: 16px;
        }
        .btn {
            background-color: #3B82F6;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        .btn:hover {
            background-color: #2563EB;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .current-user {
            background-color: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Simple Login/Register Test</h1>
        
        <?php if ($message): ?>
            <div class="success"><?php echo $message; ?></div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="error"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <!-- Current Session Status -->
        <div class="info">
            <h3>Current Session Status</h3>
            <?php if (isLoggedIn()): ?>
                <?php $user = getCurrentUser(); ?>
                <div class="current-user">
                    <strong>Logged in as:</strong> <?php echo htmlspecialchars($user['name']); ?> (<?php echo htmlspecialchars($user['email']); ?>)<br>
                    <strong>Role:</strong> <?php echo htmlspecialchars($user['role']); ?><br>
                    <a href="?logout=1">Logout</a>
                </div>
            <?php else: ?>
                <p>Not logged in</p>
            <?php endif; ?>
        </div>
        
        <!-- Registration Form -->
        <div class="form-section">
            <h2>Register New User</h2>
            <form method="POST">
                <div class="form-group">
                    <label for="reg_name">Full Name:</label>
                    <input type="text" id="reg_name" name="name" required>
                </div>
                <div class="form-group">
                    <label for="reg_email">Email:</label>
                    <input type="email" id="reg_email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="reg_password">Password:</label>
                    <input type="password" id="reg_password" name="password" required minlength="6">
                </div>
                <div class="form-group">
                    <label for="reg_confirm_password">Confirm Password:</label>
                    <input type="password" id="reg_confirm_password" name="confirm_password" required minlength="6">
                </div>
                <button type="submit" name="register" class="btn">Register</button>
            </form>
        </div>
        
        <!-- Login Form -->
        <div class="form-section">
            <h2>Login</h2>
            <form method="POST">
                <div class="form-group">
                    <label for="login_email">Email:</label>
                    <input type="email" id="login_email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="login_password">Password:</label>
                    <input type="password" id="login_password" name="password" required>
                </div>
                <button type="submit" name="login" class="btn">Login</button>
            </form>
            
            <div style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
                <strong>Default Admin Credentials:</strong><br>
                Email: <EMAIL><br>
                Password: admin123
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="form-section">
            <h2>Quick Actions</h2>
            <p><a href="dashboard/register.php">Go to Real Registration Page</a></p>
            <p><a href="dashboard/login.php">Go to Real Login Page</a></p>
            <p><a href="fix-auth.php">Authentication Fix Tool</a></p>
            <p><a href="verify-setup.php">Setup Verification</a></p>
            <p><a href="index.php">Homepage</a></p>
        </div>
        
        <!-- Debug Information -->
        <div class="form-section">
            <h2>Debug Information</h2>
            <p><strong>Session ID:</strong> <?php echo session_id(); ?></p>
            <p><strong>Session Status:</strong> <?php echo session_status() === PHP_SESSION_ACTIVE ? 'Active' : 'Inactive'; ?></p>
            
            <h4>Database Connection Test:</h4>
            <?php
            try {
                $db = getDB();
                echo '<p style="color: green;">✓ Database connected successfully</p>';
                
                $stmt = $db->query("SELECT COUNT(*) as count FROM users");
                $result = $stmt->fetch();
                echo '<p>Total users in database: ' . $result['count'] . '</p>';
                
            } catch (Exception $e) {
                echo '<p style="color: red;">✗ Database error: ' . $e->getMessage() . '</p>';
            }
            ?>
            
            <h4>Required Functions Check:</h4>
            <?php
            $functions = ['isLoggedIn', 'getCurrentUser', 'generateCSRFToken', 'verifyCSRFToken', 'sanitizeInput'];
            foreach ($functions as $func) {
                if (function_exists($func)) {
                    echo '<p style="color: green;">✓ ' . $func . '() exists</p>';
                } else {
                    echo '<p style="color: red;">✗ ' . $func . '() missing</p>';
                }
            }
            ?>
            
            <h4>Constants Check:</h4>
            <?php
            $constants = ['SITE_NAME', 'DB_NAME', 'PASSWORD_MIN_LENGTH'];
            foreach ($constants as $const) {
                if (defined($const)) {
                    echo '<p style="color: green;">✓ ' . $const . ' = ' . constant($const) . '</p>';
                } else {
                    echo '<p style="color: red;">✗ ' . $const . ' not defined</p>';
                }
            }
            ?>
        </div>
    </div>
</body>
</html>

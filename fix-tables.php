<?php
/**
 * Table Fix Script
 * This will definitely create the required tables
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/config.php';

// Force create tables function
function forceCreateTables() {
    try {
        $db = getDB();

        // Make absolutely sure we're using the right database
        $db->exec("USE `" . DB_NAME . "`");

        echo "<h3>Creating Tables...</h3>";

        // Disable foreign key checks to allow dropping tables in any order
        $db->exec("SET FOREIGN_KEY_CHECKS = 0");
        echo "<p>ℹ Disabled foreign key checks</p>";

        // Drop existing tables if they exist (to start fresh)
        // Order doesn't matter now since foreign key checks are disabled
        $dropTables = [
            'DROP TABLE IF EXISTS orders',
            'DROP TABLE IF EXISTS products',
            'DROP TABLE IF EXISTS stores',
            'DROP TABLE IF EXISTS user_sessions',
            'DROP TABLE IF EXISTS settings',
            'DROP TABLE IF EXISTS users'
        ];

        foreach ($dropTables as $dropSql) {
            try {
                $db->exec($dropSql);
                echo "<p>✓ Dropped existing table</p>";
            } catch (Exception $e) {
                echo "<p>ℹ No existing table to drop: " . $e->getMessage() . "</p>";
            }
        }

        // Re-enable foreign key checks
        $db->exec("SET FOREIGN_KEY_CHECKS = 1");
        echo "<p>ℹ Re-enabled foreign key checks</p>";
        
        // Create users table
        $usersSql = "CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(150) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            role ENUM('user', 'admin') DEFAULT 'user',
            avatar_path VARCHAR(255) DEFAULT NULL,
            email_verified TINYINT(1) DEFAULT 0,
            status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_email (email),
            INDEX idx_role (role),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        try {
            $db->exec($usersSql);
            echo "<p class='success'>✓ Users table created</p>";
        } catch (Exception $e) {
            echo "<p class='error'>✗ Error creating users table: " . $e->getMessage() . "</p>";
            throw $e;
        }
        
        // Create stores table
        $storesSql = "CREATE TABLE stores (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            store_name VARCHAR(100) NOT NULL,
            slug VARCHAR(100) UNIQUE NOT NULL,
            description TEXT,
            logo_path VARCHAR(255) DEFAULT NULL,
            banner_path VARCHAR(255) DEFAULT NULL,
            theme_color VARCHAR(7) DEFAULT '#3B82F6',
            accent_color VARCHAR(7) DEFAULT '#10B981',
            custom_css TEXT,
            is_active TINYINT(1) DEFAULT 1,
            total_products INT DEFAULT 0,
            total_orders INT DEFAULT 0,
            total_revenue DECIMAL(10,2) DEFAULT 0.00,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_user_id (user_id),
            INDEX idx_slug (slug),
            INDEX idx_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        try {
            $db->exec($storesSql);
            echo "<p class='success'>✓ Stores table created</p>";
        } catch (Exception $e) {
            echo "<p class='error'>✗ Error creating stores table: " . $e->getMessage() . "</p>";
            throw $e;
        }

        // Create products table
        $productsSql = "CREATE TABLE products (
            id INT AUTO_INCREMENT PRIMARY KEY,
            store_id INT NOT NULL,
            name VARCHAR(150) NOT NULL,
            description TEXT,
            design_path VARCHAR(255) NOT NULL,
            mockup_path VARCHAR(255) NOT NULL,
            base_price DECIMAL(8,2) NOT NULL DEFAULT 19.99,
            sizes TEXT NOT NULL,
            colors TEXT NOT NULL,
            design_config TEXT,
            tags VARCHAR(255),
            is_active TINYINT(1) DEFAULT 1,
            views_count INT DEFAULT 0,
            orders_count INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (store_id) REFERENCES stores(id) ON DELETE CASCADE,
            INDEX idx_store_id (store_id),
            INDEX idx_active (is_active),
            INDEX idx_created (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        try {
            $db->exec($productsSql);
            echo "<p class='success'>✓ Products table created</p>";
        } catch (Exception $e) {
            echo "<p class='error'>✗ Error creating products table: " . $e->getMessage() . "</p>";
            throw $e;
        }

        // Create orders table
        $ordersSql = "CREATE TABLE orders (
            id INT AUTO_INCREMENT PRIMARY KEY,
            order_number VARCHAR(20) UNIQUE NOT NULL,
            product_id INT NOT NULL,
            customer_name VARCHAR(100) NOT NULL,
            customer_email VARCHAR(150) NOT NULL,
            customer_phone VARCHAR(20),
            shipping_address TEXT NOT NULL,
            city VARCHAR(50) NOT NULL,
            state VARCHAR(50) NOT NULL,
            postal_code VARCHAR(20) NOT NULL,
            country VARCHAR(50) DEFAULT 'India',
            selected_size VARCHAR(10) NOT NULL,
            selected_color VARCHAR(50) NOT NULL,
            quantity INT NOT NULL DEFAULT 1,
            unit_price DECIMAL(8,2) NOT NULL,
            total_amount DECIMAL(8,2) NOT NULL,
            status ENUM('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending',
            payment_method ENUM('cod', 'online', 'test') DEFAULT 'cod',
            payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
            notes TEXT,
            tracking_number VARCHAR(100),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
            INDEX idx_product_id (product_id),
            INDEX idx_status (status),
            INDEX idx_order_number (order_number),
            INDEX idx_created (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        try {
            $db->exec($ordersSql);
            echo "<p class='success'>✓ Orders table created</p>";
        } catch (Exception $e) {
            echo "<p class='error'>✗ Error creating orders table: " . $e->getMessage() . "</p>";
            throw $e;
        }

        // Create user_sessions table
        $sessionsSql = "CREATE TABLE user_sessions (
            id VARCHAR(128) PRIMARY KEY,
            user_id INT NOT NULL,
            ip_address VARCHAR(45),
            user_agent TEXT,
            last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_user_id (user_id),
            INDEX idx_last_activity (last_activity)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        try {
            $db->exec($sessionsSql);
            echo "<p class='success'>✓ User sessions table created</p>";
        } catch (Exception $e) {
            echo "<p class='error'>✗ Error creating user_sessions table: " . $e->getMessage() . "</p>";
            throw $e;
        }

        // Create settings table
        $settingsSql = "CREATE TABLE settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_key (setting_key)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        try {
            $db->exec($settingsSql);
            echo "<p class='success'>✓ Settings table created</p>";
        } catch (Exception $e) {
            echo "<p class='error'>✗ Error creating settings table: " . $e->getMessage() . "</p>";
            throw $e;
        }

        // Create default admin user
        try {
            $adminSql = "INSERT INTO users (name, email, password, role, email_verified, status) VALUES (?, ?, ?, ?, ?, ?)";
            $stmt = $db->prepare($adminSql);
            $stmt->execute([
                'Super Admin',
                '<EMAIL>',
                '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // admin123
                'admin',
                1,
                'active'
            ]);
            echo "<p class='success'>✓ Default admin user created</p>";
        } catch (Exception $e) {
            echo "<p class='error'>✗ Error creating admin user: " . $e->getMessage() . "</p>";
            throw $e;
        }
        
        // Insert default settings
        try {
            $defaultSettings = [
                ['site_name', 'PrintShop Platform', 'Website name'],
                ['site_description', 'Multi-tenant print-on-demand platform', 'Website description'],
                ['default_commission_rate', '5.00', 'Default referral commission rate'],
                ['max_file_size', '5242880', 'Maximum file upload size in bytes (5MB)'],
                ['allowed_file_types', 'jpg,jpeg,png,gif', 'Allowed image file extensions'],
                ['default_product_price', '19.99', 'Default product base price'],
                ['currency_symbol', '₹', 'Currency symbol'],
                ['timezone', 'Asia/Kolkata', 'Default timezone']
            ];

            $stmt = $db->prepare("INSERT INTO settings (setting_key, setting_value, description) VALUES (?, ?, ?)");
            foreach ($defaultSettings as $setting) {
                $stmt->execute($setting);
            }
            echo "<p class='success'>✓ Default settings created</p>";
        } catch (Exception $e) {
            echo "<p class='error'>✗ Error creating default settings: " . $e->getMessage() . "</p>";
            throw $e;
        }

        // Verify all tables were created
        echo "<h3>Verifying Tables...</h3>";
        $requiredTables = ['users', 'stores', 'products', 'orders', 'user_sessions', 'settings'];
        $missingTables = [];

        foreach ($requiredTables as $table) {
            $stmt = $db->prepare("SHOW TABLES LIKE ?");
            $stmt->execute([$table]);
            if ($stmt->rowCount() > 0) {
                echo "<p class='success'>✓ Table '$table' exists</p>";
            } else {
                echo "<p class='error'>✗ Table '$table' missing</p>";
                $missingTables[] = $table;
            }
        }

        if (empty($missingTables)) {
            echo "<p class='success'>✓ All tables verified successfully!</p>";
            return true;
        } else {
            echo "<p class='error'>✗ Missing tables: " . implode(', ', $missingTables) . "</p>";
            return false;
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>✗ Error: " . $e->getMessage() . "</p>";
        return false;
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Tables</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .success { color: #155724; }
        .error { color: #721c24; }
        .btn {
            background-color: #3B82F6;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover { background-color: #2563EB; }
        .btn.danger { background-color: #DC2626; }
        .btn.danger:hover { background-color: #B91C1C; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Fix Tables Script</h1>
        
        <p><strong>Warning:</strong> This will drop all existing tables and recreate them fresh. All data will be lost!</p>
        
        <?php
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['force_create'])) {
            echo '<div style="border: 2px solid #DC2626; padding: 20px; margin: 20px 0; background-color: #FEF2F2;">';
            echo '<h3 style="color: #DC2626;">FORCE CREATING TABLES...</h3>';
            
            if (forceCreateTables()) {
                echo '<h3 style="color: #059669;">✓ ALL TABLES CREATED SUCCESSFULLY!</h3>';
                echo '<p>You can now:</p>';
                echo '<ul>';
                echo '<li>Go to <a href="index.php">Homepage</a></li>';
                echo '<li>Try <a href="dashboard/register.php">Registration</a></li>';
                echo '<li><NAME_EMAIL> / admin123</li>';
                echo '<li>Run <a href="test-auth.php">Authentication Test</a></li>';
                echo '</ul>';
            } else {
                echo '<h3 style="color: #DC2626;">✗ TABLE CREATION FAILED</h3>';
                echo '<p>Check the error messages above and try manual creation in phpMyAdmin.</p>';
            }
            
            echo '</div>';
        } else {
            echo '<p>This script will:</p>';
            echo '<ul>';
            echo '<li>Drop all existing tables (if any)</li>';
            echo '<li>Create fresh tables with correct structure</li>';
            echo '<li>Create default admin user (<EMAIL> / admin123)</li>';
            echo '<li>Insert default settings</li>';
            echo '</ul>';
            
            echo '<form method="POST">';
            echo '<button type="submit" name="force_create" class="btn danger" onclick="return confirm(\'Are you sure? This will delete all existing data!\')">Force Create Tables</button>';
            echo '</form>';
        }
        ?>
        
        <div style="margin-top: 30px;">
            <a href="debug-tables.php" class="btn">Debug Tables</a>
            <a href="setup.php" class="btn">Run Setup</a>
            <a href="test-auth.php" class="btn">Test Auth</a>
            <a href="index.php" class="btn">Homepage</a>
        </div>
    </div>
</body>
</html>
